<script setup lang="ts">
import { Star } from 'lucide-vue-next'
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import InputField from '@/components/shared/Form/InputField.vue'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useGlobalDialog } from '@/composables/useGlobalDialog'
import { convertStarToTon, getTonReceive, verifyStarConversion } from '@/repositories/starPayment'
import { useUserStore } from '@/stores/userStore'
import { debounce, delay, retryAsync } from '@/utils'

interface Props {
  open?: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', data: { starAmount: number, tonAmount: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
})

const emit = defineEmits<Emits>()

const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const { t } = useI18n()
const { showSuccess, showError } = useGlobalDialog()
const MINIMUM_STARS = 10

const form = reactive({
  starAmount: 0,
})

const errors = reactive({
  starAmount: '',
})

const isLoading = ref(false)
const isFetchingTon = ref(false)
const conversionError = ref('')
const estimatedTon = ref(0)

// Verification modal state
const isVerificationModalOpen = ref(false)
const verificationStatus = ref<'verifying' | 'success' | 'failed'>('verifying')
const verificationMessage = ref('')
const currentAttempt = ref(0)
const maxAttempts = ref(3)

const fetchTonAmount = debounce(async (stars: number) => {
  if (stars <= 0) {
    estimatedTon.value = 0
    return
  }

  isFetchingTon.value = true
  try {
    const nanoTon = await getTonReceive(stars)
    estimatedTon.value = nanoTon / 1_000_000_000
  }
  catch (error) {
    console.error(error)
    estimatedTon.value = 0
  }
  finally {
    isFetchingTon.value = false
  }
}, 500)

// Watch for star amount changes to clear errors
watch(() => form.starAmount, (newAmount) => {
  if (errors.starAmount) {
    errors.starAmount = ''
  }
  fetchTonAmount(newAmount)
})

async function handleConvert() {
  // Clear previous errors
  clearErrors()

  if (!validateForm()) {
    return
  }

  isLoading.value = true
  conversionError.value = ''

  try {
    const response = await convertStarToTon(form.starAmount)
    const transactionHash = response.transaction_hash

    const tonAmount = estimatedTon.value

    // Open the invoice using Telegram WebApp API
    if (response.invoice_link && window.Telegram?.WebApp?.openInvoice) {
      window.Telegram.WebApp.openInvoice(response.invoice_link, async (status: string) => {
        if (status === 'paid') {
          await delay(1000)

          // Show verification modal
          showVerificationModal()

          try {
            const success = await retryAsync(() => verifyStarConversion(transactionHash), {
              retries: 3,
              delay: 2000,
              exponentialBackoff: true,
              onRetry: (error, attempt) => {
                console.warn(`Star conversion verification attempt ${attempt} failed:`, error.message)
                updateVerificationStatus(
                  'verifying',
                  t('starConversion.verification.retrying', {
                    attempt,
                    maxAttempts: maxAttempts.value,
                  }),
                  attempt,
                )
              },
            })

            if (success) {
              // Show success in verification modal
              updateVerificationStatus('success', t('starConversion.verification.success'))

              // Wait a moment to show success, then close verification modal
              await delay(1500)
              closeVerificationModal()

              // Close main dialog and show final success
              closeDialog()
              emit('success', {
                starAmount: form.starAmount,
                tonAmount,
              })
              showSuccess(
                t('starConversion.success.title'),
                t('starConversion.success.message', {
                  starAmount: form.starAmount,
                  tonAmount: tonAmount.toFixed(6),
                }),
              )
              await useUserStore().refreshUserData()
            }
            else {
              // Show failure in verification modal
              updateVerificationStatus('failed', t('starConversion.verification.failed'))
            }
          }
          catch (error: any) {
            console.error('Verification error:', error)
            updateVerificationStatus('failed', t('starConversion.verification.failed'))
          }
        }
        else if (status === 'cancelled') {
          showError(
            t('starConversion.error.title'),
            t('starConversion.error.cancelled', { default: 'Payment was cancelled' }),
          )
        }
        else if (status === 'failed') {
          showError(
            t('starConversion.error.title'),
            t('starConversion.error.failed', { default: 'Payment failed' }),
          )
        }
      })
    }
    else {
      // Fallback: if Telegram WebApp is not available or no invoice link
      console.warn('Telegram WebApp openInvoice not available or no invoice link')

      // Success: close dialog and reset form
      closeDialog()

      // Emit success event with conversion data
      emit('success', {
        starAmount: form.starAmount,
        tonAmount,
      })

      // Show success notification
      showSuccess(
        t('starConversion.success.title'),
        t('starConversion.success.message', {
          starAmount: form.starAmount.toLocaleString(),
          tonAmount: tonAmount.toFixed(6),
        }),
      )

      // TODO: Refresh user balance
    }
  }
  catch (error: any) {
    const errorMessage = error.message || t('starConversion.error.message')
    conversionError.value = errorMessage

    // Show error notification
    showError(
      t('starConversion.error.title'),
      errorMessage,
    )
  }
  finally {
    isLoading.value = false
  }
}

function clearErrors() {
  errors.starAmount = ''
  conversionError.value = ''
}

function validateForm() {
  clearErrors()

  if (!form.starAmount || form.starAmount <= 0) {
    errors.starAmount = t('starConversion.amount.required')
  }

  if (form.starAmount < MINIMUM_STARS) {
    errors.starAmount = t('starConversion.amount.minimum', { minimum: MINIMUM_STARS })
  }

  return Object.values(errors).every(error => error === '')
}

function closeDialog() {
  isOpen.value = false
  form.starAmount = 0
  clearErrors()
}

// Verification modal functions
function showVerificationModal() {
  isVerificationModalOpen.value = true
  verificationStatus.value = 'verifying'
  verificationMessage.value = t('starConversion.verification.verifying')
  currentAttempt.value = 0
}

function updateVerificationStatus(status: 'verifying' | 'success' | 'failed', message?: string, attempt?: number) {
  verificationStatus.value = status
  if (message) {
    verificationMessage.value = message
  }
  if (attempt !== undefined) {
    currentAttempt.value = attempt
  }
}

function closeVerificationModal() {
  isVerificationModalOpen.value = false
  verificationStatus.value = 'verifying'
  verificationMessage.value = ''
  currentAttempt.value = 0
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-md text-white border-white/20">
      <DialogHeader>
        <div class="flex items-center justify-center">
          <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-blue-500 rounded-full flex items-center justify-center">
            <Star class="w-6 h-6 text-white fill-current" />
          </div>
        </div>
        <DialogTitle class="text-center text-lg font-medium text-white">
          {{ t('starConversion.title') }}
        </DialogTitle>
        <DialogDescription class="text-center text-gray-400 text-sm">
          {{ t('starConversion.description') }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- General Error Message -->
        <div v-if="conversionError" class="p-3 bg-red-900/50 border border-red-700 rounded-md text-red-200 text-sm">
          {{ conversionError }}
        </div>

        <!-- Conversion Rate Info -->
        <div class="p-3 bg-blue-900/30 border border-blue-700/50 rounded-md text-blue-200 text-sm text-center">
          {{ t('starConversion.conversionRate') }}
        </div>

        <!-- Star Amount Input -->
        <InputField
          v-model="form.starAmount"
          :label="t('starConversion.starAmount')"
          type="number"
          :placeholder="t('starConversion.starAmountPlaceholder')"
          :error="errors.starAmount"
          :helper-text="t('starConversion.amount.minimum', { minimum: MINIMUM_STARS })"
          :disabled="isLoading"
        />

        <!-- Estimated TON Display -->
        <div v-if="form.starAmount > 0" class="space-y-2">
          <label class="text-sm text-white font-medium">
            {{ t('starConversion.estimatedTon') }}
          </label>
          <div class="p-3 bg-gray-800 border border-gray-600 rounded-md text-white font-mono flex items-center">
            <template v-if="isFetchingTon">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              <span>{{ t('starConversion.fetchingTon') }}</span>
            </template>
            <template v-else>
              {{ estimatedTon.toFixed(6) }} TON
            </template>
          </div>
        </div>
      </div>

      <DialogFooter class="space-y-2">
        <Button
          variant="ghost"
          class="w-full text-gray-400 hover:text-white hover:bg-gray-700"
          :disabled="isLoading"
          @click="closeDialog"
        >
          {{ t('common.back') }}
        </Button>
        <Button
          class="w-full bg-gradient-to-r from-yellow-500 to-blue-500 hover:from-yellow-600 hover:to-blue-600"
          :disabled="isLoading || isFetchingTon || !form.starAmount || form.starAmount <= 0"
          @click="handleConvert"
        >
          {{ isLoading ? t('starConversion.processing') : t('starConversion.convertButton') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- Verification Modal -->
  <Dialog v-model:open="isVerificationModalOpen">
    <DialogContent class="sm:max-w-md text-white border-white/20">
      <DialogHeader>
        <div class="flex items-center justify-center">
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <!-- Loading spinner for verifying state -->
            <svg
              v-if="verificationStatus === 'verifying'"
              class="animate-spin h-6 w-6 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
            <!-- Success icon -->
            <svg
              v-else-if="verificationStatus === 'success'"
              class="h-6 w-6 text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <!-- Error icon -->
            <svg
              v-else
              class="h-6 w-6 text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        </div>
        <DialogTitle class="text-center text-lg font-medium text-white">
          {{ t('starConversion.verification.title') }}
        </DialogTitle>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- Verification Message -->
        <div class="text-center">
          <p class="text-gray-300 text-sm">
            {{ verificationMessage }}
          </p>

          <!-- Retry attempt counter -->
          <p v-if="currentAttempt > 0 && verificationStatus === 'verifying'" class="text-gray-400 text-xs mt-2">
            {{ t('starConversion.verification.retrying', { attempt: currentAttempt, maxAttempts }) }}
          </p>
        </div>

        <!-- Progress indicator for verifying state -->
        <div v-if="verificationStatus === 'verifying'" class="w-full bg-gray-700 rounded-full h-2">
          <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full animate-pulse" style="width: 60%" />
        </div>
      </div>

      <DialogFooter v-if="verificationStatus !== 'verifying'">
        <Button
          v-if="verificationStatus === 'failed'"
          variant="destructive"
          class="w-full"
          @click="closeVerificationModal"
        >
          {{ t('common.back') }}
        </Button>
        <!-- Success state will auto-close, so no button needed -->
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
