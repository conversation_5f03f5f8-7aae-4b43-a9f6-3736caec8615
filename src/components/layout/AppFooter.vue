<script setup lang="ts">
import { FlameIcon, HouseIcon, UsersIcon, WalletIcon } from 'lucide-vue-next'
import { computed } from 'vue'
import { RouterLink, useRoute } from 'vue-router'

const route = useRoute()
const currentPath = computed(() => route.path)
const navItems = [
  {
    label: 'Home',
    icon: HouseIcon,
    path: '/',
  },
  // {
  //   label: 'Rewards',
  //   icon: FlameIcon,
  //   path: '/rewards',
  // },
  {
    label: 'Wallet',
    icon: WalletIcon,
    path: '/wallet',
  },
  // {
  //   label: 'Top Users',
  //   icon: UsersIcon,
  //   path: '/top-users',
  // },
]
</script>

<template>
  <footer class="fixed bottom-0 left-0 w-full bg-background py-3 px-2 z-20 border-t border-gray-800">
    <nav class="max-w-lg mx-auto flex justify-center gap-6 items-center">
      <RouterLink
        v-for="item in navItems"
        :key="item.path"
        :to="item.path"
        class="flex flex-col items-center"
      >
        <div class="rounded-full p-2" :class="[currentPath === item.path ? 'text-primary' : 'text-gray-400']">
          <component :is="item.icon" class="h-5 w-5" />
        </div>
        <span class="text-xs font-medium" :class="[currentPath === item.path ? 'text-white' : 'text-gray-400']">{{ item.label }}</span>
      </RouterLink>
    </nav>
  </footer>
</template>
