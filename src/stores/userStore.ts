import type { UserData } from '@/types/user'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { me } from '@/repositories/user'

export const useUserStore = defineStore('user', () => {
  const user = ref<UserData | null>(null)
  const accessToken = ref<string | null>(null)

  // Computed property to check if user is authenticated
  const isAuthenticated = computed(() => {
    return !!accessToken.value
  })

  function setUser(userData: UserData) {
    user.value = userData
  }

  function setAccessToken(token: string) {
    accessToken.value = token
  }

  // Set authentication data from API response
  function setAuthData(token: string, userData?: UserData) {
    setAccessToken(token)
    if (userData) {
      setUser(userData)
    }
  }

  // Clear all authentication data
  function clearAuthData() {
    user.value = null
    accessToken.value = null
  }

  async function refreshUserData() {
    console.log('refresh user data', isAuthenticated.value)
    if (isAuthenticated.value) {
      const response = await me()
      console.log('user data', response)
      setUser(response)
    }
  }

  return {
    user,
    accessToken,
    isAuthenticated,

    setUser,
    setAccessToken,
    setAuthData,
    clearAuthData,
    refreshUserData,
  }
})
