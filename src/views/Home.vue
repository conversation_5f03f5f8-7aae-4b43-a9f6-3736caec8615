<script setup lang="ts">
import BalanceSection from '@/components/Home/BalanceSection.vue'
import ClaimSection from '@/components/Home/ClaimSection.vue'
import ConvertStarButton from '@/components/Home/ConvertStarButton.vue'
import MiningSection from '@/components/Home/MiningSection.vue'
import SelectLang from '@/components/Home/SelectLang.vue'
import UserInfo from '@/components/Home/UserInfo.vue'

// Handle convert star button click
function handleConvertStar() {
  // Add your conversion logic here
  // Example: navigate to conversion page, open dialog, or call API
  // You can add navigation, API calls, or dialog opening logic here
}
</script>

<template>
  <div class="pb-8">
    <!-- User Profile Section -->
    <section class="flex justify-between items-center">
      <UserInfo />
      <SelectLang />
    </section>

    <BalanceSection class="mt-3" />

    <section class="mt-8">
      <ConvertStarButton @click="handleConvertStar" />
    </section>

    <MiningSection class="mt-8" />

    <ClaimSection class="mt-8" />
  </div>
</template>
