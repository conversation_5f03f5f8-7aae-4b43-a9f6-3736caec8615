# Star Conversion Dialog Testing Mode

This document explains how to use the testing mode for the StarConversionDialog component to test UI functionality without making real payments or API calls.

## Overview

The StarConversionDialog component now includes a comprehensive testing mode that allows you to:

- ✅ Mock payment processing logic
- ✅ Skip actual backend API calls
- ✅ Simulate successful and failed responses
- ✅ Test all UI states (loading, success, error, verification)
- ✅ Preview the complete payment flow without real transactions

## Enabling Testing Mode

Testing mode is automatically enabled when any of these conditions are met:

1. **Environment Variable**: Set `VITE_TESTING_MODE=true` in your `.env` file
2. **Development Environment**: `VITE_ENV=development` 
3. **Localhost**: Running on `localhost`

### Manual Configuration

Add to your `.env` file:
```env
# Enable testing mode
VITE_TESTING_MODE=true
```

## Testing Features

### 🧪 Visual Indicators

When testing mode is active, you'll see:
- Purple banner: "🧪 Testing Mode Active - No real payments will be processed"
- Testing controls section with simulation buttons

### 🎮 Testing Controls

The dialog includes special testing controls:

1. **✅ Success Button**: Simulates a successful payment flow
2. **❌ Error Button**: Simulates a payment error
3. **Normal Flow**: Use the regular "Convert" button to test the full verification flow

### 🔄 Simulated Behaviors

#### API Calls
- `getTonReceive()`: Returns mock conversion rate (1 star = 0.001 TON)
- `convertStarToTon()`: Returns mock transaction hash and invoice link
- `verifyStarConversion()`: Simulates verification with 70% success rate

#### Timing
- TON amount fetching: ~800ms delay
- Payment processing: ~1200ms delay  
- Verification: ~1500ms delay with retry logic

#### User Store
- Skips `useUserStore().refreshUserData()` calls
- Prevents actual balance updates

## Testing Scenarios

### 1. Successful Payment Flow
1. Enter star amount (≥10)
2. Click "Convert Stars to TON" 
3. Watch verification modal with retry attempts
4. See success notification

### 2. Quick Success Test
1. Enter star amount
2. Click "✅ Success" button
3. Immediate success simulation

### 3. Error Handling
1. Enter star amount
2. Click "❌ Error" button
3. See error message display

### 4. Verification Retry Logic
1. Use normal convert flow
2. Mock verification has 30% failure rate
3. Watch retry attempts (up to 3 times)
4. See retry status messages

## UI States to Test

### Loading States
- ⏳ Fetching TON conversion rate
- ⏳ Processing payment
- ⏳ Verifying transaction

### Success States
- ✅ Conversion rate loaded
- ✅ Payment processed
- ✅ Transaction verified
- ✅ Final success notification

### Error States
- ❌ Invalid input (< 10 stars)
- ❌ Network simulation errors
- ❌ Verification failures
- ❌ Payment cancellation

### Verification Modal
- 🔄 Verifying status with spinner
- 🔄 Retry attempts with counter
- ✅ Success with checkmark
- ❌ Failed with error icon

## Console Output

Testing mode provides helpful console messages:
```
🧪 Testing Mode: Simulating payment flow
🧪 Mock verification attempt 1 failed: Mock verification failed - testing retry logic
🧪 Testing Mode: Skipping user store refresh
```

## Disabling Testing Mode

To return to production mode:

1. Remove or set `VITE_TESTING_MODE=false` in `.env`
2. Ensure `VITE_ENV=production` for production builds
3. Deploy to non-localhost domain

## Development Tips

### Testing Different Scenarios
- Refresh the page between tests to reset state
- Try different star amounts to test validation
- Use browser dev tools to inspect network calls (should be none in testing mode)

### Customizing Mock Behavior
Edit the mock functions in `StarConversionDialog.vue`:
- `mockGetTonReceive()`: Adjust conversion rates
- `mockConvertStarToTon()`: Modify response format
- `mockVerifyStarConversion()`: Change success/failure rates

### Adding New Test Cases
You can extend the testing controls by:
1. Adding new simulation functions
2. Creating additional test buttons
3. Implementing specific error scenarios

## Production Safety

⚠️ **Important**: Testing mode is automatically disabled in production environments to ensure real payments work correctly.

The component checks multiple conditions to prevent accidental testing mode activation in production:
- Environment variables
- Hostname detection
- Build environment detection
